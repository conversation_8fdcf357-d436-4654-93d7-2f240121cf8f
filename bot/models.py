"""
数据模型定义
"""
from typing import Optional, List, Any, Dict
from pydantic import BaseModel
from enum import IntEnum


class MessageType(IntEnum):
    """消息类型枚举"""
    文本 = 1
    图片 = 3
    位置 = 19
    语音 = 34
    名片 = 42
    视频 = 43
    表情 = 47
    应用 = 49
    打开 = 51
    文件 = 62

class MessageType_49(IntEnum):
    """消息类型枚举"""
    链接 = 5
    文件 = 6
    小程序 = 33
    群公告 = 87
    转账 = 2000
    红包 = 2001


class MessageVersion(IntEnum):
    """消息版本"""
    V1 = 1      # wechatpadpro, wx_msg
    V1_5 = 1    # wx_msg (实际上也是1，但处理逻辑略有不同)
    V2 = 2      # wxapi


class StandardMessage(BaseModel):
    """标准化后的消息格式"""
    msg_id: int
    msg_type: int
    timestamp: int
    is_self_message: bool
    wxid: str
    uuid: str
    from_user_name: str
    to_user_name: str
    content: str
    push_content: str
    msg_source: str
    ver: int
    
    # 群组相关字段（处理后添加）
    group_id: Optional[str] = None
    at_list: Optional[List[str]] = None
    member_count: Optional[int] = None


class RawMessageV1(BaseModel):
    """版本1原始消息格式 (wechatpadpro)"""
    msg_id: int
    msg_type: int
    create_time: int
    is_self_message: bool
    account_wxid: str
    account_uuid: str
    from_user_name: str
    to_user_name: str
    content: str
    push_content: str
    msg_source: str


class RawMessageV1_5(BaseModel):
    """版本1.5原始消息格式 (wx_msg)"""
    AddMsgs: List[Dict[str, Any]]
    userName: str
    UUID: str


class RawMessageV2(BaseModel):
    """版本2原始消息格式 (wxapi)"""
    Data: Dict[str, Any]

