{"name": "wechat_bot_recv", "nodes": [{"parameters": {"queue": "wechatpadpro", "options": {"jsonParseBody": true, "onlyContent": true}}, "type": "n8n-nodes-base.rabbitmqTrigger", "typeVersion": 1, "position": [0, 20], "id": "c159d259-1aa9-436a-a013-f2cfe20cc8ce", "name": "MQ V1", "credentials": {"rabbitmq": {"id": "tuXX9zoldmdlOKck", "name": "RabbitMQ account"}}}, {"parameters": {"queue": "wxapi", "options": {"jsonParseBody": true, "onlyContent": true}}, "type": "n8n-nodes-base.rabbitmqTrigger", "typeVersion": 1, "position": [0, 420], "id": "156a3725-aebd-45ab-ad6e-45faa5d5abb9", "name": "MQ V2", "credentials": {"rabbitmq": {"id": "tuXX9zoldmdlOKck", "name": "RabbitMQ account"}}}, {"parameters": {"url": "http://**************:15672/api/queues", "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "options": {}}, "name": "HTTP Request - Get All Queues", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [220, -240], "id": "ec0380b0-4e46-4f88-b0fa-c7f961f7fe90", "credentials": {"httpBasicAuth": {"id": "nkggcP66VbDeiPOX", "name": "Unnamed credential"}}}, {"parameters": {"method": "DELETE", "url": "=http://**************:15672/api/queues/%2F/{{ $input.item.json[\"name\"] }}/contents", "authentication": "genericCredentialType", "genericAuthType": "httpBasicAuth", "options": {}}, "name": "HTTP Request - <PERSON>urge Queue", "type": "n8n-nodes-base.httpRequest", "typeVersion": 3, "position": [660, -240], "id": "c7fc3f9c-882a-43d2-bc92-94ec6493a46b", "credentials": {"httpBasicAuth": {"id": "nkggcP66VbDeiPOX", "name": "Unnamed credential"}}}, {"parameters": {"jsCode": "const queues = $input.all().filter(queue => queue.json.name.startsWith(\"wx_messages\"));\n\nreturn queues.map(queue => ({\n  name: encodeURIComponent(queue.json.name)\n}));"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [440, -240], "id": "e3009533-8dff-4e5a-80a4-9735cf8b6afc", "name": "Code"}, {"parameters": {"rule": {"interval": [{"field": "minutes"}]}}, "type": "n8n-nodes-base.scheduleTrigger", "typeVersion": 1.2, "position": [0, -240], "id": "c2825fea-0a99-4ec1-9548-c6f6e23b8fdc", "name": "Schedule Trigger"}, {"parameters": {"queue": "wx_msg", "options": {"jsonParseBody": true, "onlyContent": true}}, "type": "n8n-nodes-base.rabbitmqTrigger", "typeVersion": 1, "position": [0, 220], "id": "a4f8fcd4-d3b9-468b-8b90-f499b9593e30", "name": "MQ V1.5", "credentials": {"rabbitmq": {"id": "tuXX9zoldmdlOKck", "name": "RabbitMQ account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "caa5d346-a4c9-4f3c-bb2c-1e00c168cb78", "name": "msg_id", "value": "={{ $json.msg_id }}", "type": "number"}, {"id": "944ce9de-05da-4e48-bcf3-04c673cf0b72", "name": "msg_type", "value": "={{ $json.msg_type }}", "type": "number"}, {"id": "8d4e85a7-69cc-4e41-b5bf-a719fb14cdf1", "name": "timestamp", "value": "={{ $json.create_time }}", "type": "number"}, {"id": "da051b4b-52c5-4c01-a9db-f3a461b8353a", "name": "is_self_message", "value": "={{ $json.is_self_message }}", "type": "boolean"}, {"id": "********-f0cc-46d6-b87f-e194d48cd89d", "name": "wxid", "value": "={{ $json.account_wxid }}", "type": "string"}, {"id": "9e645f61-65da-4cd8-b7db-dbc21e769d57", "name": "uuid", "value": "={{ $json.account_uuid }}", "type": "string"}, {"id": "9ba21441-f0fd-43b8-9e83-e513db595255", "name": "from_user_name", "value": "={{ $json.from_user_name }}", "type": "string"}, {"id": "99d9a442-7b20-4097-9e9c-0c9eac1946cf", "name": "to_user_name", "value": "={{ $json.to_user_name }}", "type": "string"}, {"id": "149259ac-7a2c-4499-aa12-06e0738382b3", "name": "content", "value": "={{ $json.content }}", "type": "string"}, {"id": "58ada1c7-c663-450f-aa6c-c886056039f5", "name": "push_content", "value": "={{ $json.push_content }}", "type": "string"}, {"id": "d755058c-6fe6-488f-a633-a91b198ef9d8", "name": "msg_source", "value": "={{ $json.msg_source }}", "type": "string"}, {"id": "d8abeccc-7a68-4ca4-9b80-f3a257e0b17e", "name": "ver", "value": 1, "type": "number"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [440, 20], "id": "383d67a9-4058-4f54-8824-4326f88aed3e", "name": "整理", "notes": "整理"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "9f96e565-cb76-4731-ac6d-4f093a5c2459", "leftValue": "={{ $json.create_time }}", "rightValue": "", "operator": {"type": "number", "operation": "exists", "singleValue": true}}, {"id": "d66e3107-6ac8-4afd-9fbf-589e9b92e5db", "leftValue": "={{ $json.create_time.toDateTime('s') }}", "rightValue": "={{ $now.minus(1, 'min') }}", "operator": {"type": "dateTime", "operation": "after"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [220, 20], "id": "959bd7f0-1d0d-4781-ae10-519f52fae47c", "name": "过滤历史消息"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "44699e99-50f3-48ef-a4b1-ea49517afd34", "leftValue": "={{ $json.is_self_message }}", "rightValue": "", "operator": {"type": "boolean", "operation": "true", "singleValue": true}}], "combinator": "and"}, "renameOutput": true, "outputKey": "自己的消息"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $json.msg_type }}", "rightValue": "={{ 1 }}", "operator": {"type": "number", "operation": "equals"}, "id": "98736ed1-8c07-47a6-bba4-3c672aee2853"}], "combinator": "and"}, "renameOutput": true, "outputKey": "消息"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "058d5e44-8dc7-4adc-8ae9-f4b47bbbd4b2", "leftValue": "={{ $json.msg_type }}", "rightValue": 51, "operator": {"type": "number", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "进入聊天"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "23632a86-4fe4-4614-8e68-1a70fdc4ebc1", "leftValue": "={{ $json.msg_type }}", "rightValue": 47, "operator": {"type": "number", "operation": "equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "表情"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [880, 200], "id": "dcb64bb3-fa29-4b86-bef9-9b5174532d58", "name": "MSG_TYPE"}, {"parameters": {"operation": "removeItemsSeenInPreviousExecutions", "dedupeValue": "={{ $json.msg_id }}", "options": {"scope": "workflow", "historySize": 10000}}, "type": "n8n-nodes-base.removeDuplicates", "typeVersion": 2, "position": [660, 220], "id": "968f4546-b488-4e55-b5c0-e99438d936d8", "name": "MSG_ID"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "986f3321-8ded-484f-95ed-93c28a062a27", "leftValue": "={{ $json.AddMsgs }}", "rightValue": "", "operator": {"type": "array", "operation": "exists", "singleValue": true}}, {"id": "d66e3107-6ac8-4afd-9fbf-589e9b92e5db", "leftValue": "={{ $json.AddMsgs[0].create_time.toDateTime('s') }}", "rightValue": "={{ $now.minus(1, 'min') }}", "operator": {"type": "dateTime", "operation": "after"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [220, 220], "id": "929f5f40-f403-494a-899c-c97857535dca", "name": "过滤历史消息1"}, {"parameters": {"assignments": {"assignments": [{"id": "caa5d346-a4c9-4f3c-bb2c-1e00c168cb78", "name": "msg_id", "value": "={{ $json.AddMsgs[0].msg_id }}", "type": "number"}, {"id": "944ce9de-05da-4e48-bcf3-04c673cf0b72", "name": "msg_type", "value": "={{ $json.AddMsgs[0].msg_type }}", "type": "number"}, {"id": "8d4e85a7-69cc-4e41-b5bf-a719fb14cdf1", "name": "timestamp", "value": "={{ $json.AddMsgs[0].create_time }}", "type": "number"}, {"id": "da051b4b-52c5-4c01-a9db-f3a461b8353a", "name": "is_self_message", "value": "={{ $json.AddMsgs[0].from_user_name.str == $json.userName }}", "type": "boolean"}, {"id": "********-f0cc-46d6-b87f-e194d48cd89d", "name": "wxid", "value": "={{ $json.userName }}", "type": "string"}, {"id": "9e645f61-65da-4cd8-b7db-dbc21e769d57", "name": "uuid", "value": "={{ $json.UUID }}", "type": "string"}, {"id": "9ba21441-f0fd-43b8-9e83-e513db595255", "name": "from_user_name", "value": "={{ $json.AddMsgs[0].from_user_name.str }}", "type": "string"}, {"id": "99d9a442-7b20-4097-9e9c-0c9eac1946cf", "name": "to_user_name", "value": "={{ $json.AddMsgs[0].to_user_name.str }}", "type": "string"}, {"id": "149259ac-7a2c-4499-aa12-06e0738382b3", "name": "content", "value": "={{ $json.AddMsgs[0].content.str }}", "type": "string"}, {"id": "58ada1c7-c663-450f-aa6c-c886056039f5", "name": "push_content", "value": "={{ $json.AddMsgs[0].push_content }}", "type": "string"}, {"id": "d755058c-6fe6-488f-a633-a91b198ef9d8", "name": "msg_source", "value": "={{ $json.AddMsgs[0].msg_source }}", "type": "string"}, {"id": "475ffb06-b009-44ce-83e8-e3ec69a5b533", "name": "ver", "value": 1, "type": "number"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [440, 220], "id": "e5971b0a-9c9d-46c0-89f7-e4ef45a5289a", "name": "整理1", "notes": "整理"}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "cc01b820-d55f-45ee-9b0d-12f086d2c007", "leftValue": "={{ $json.Data.AddMsgs }}", "rightValue": "", "operator": {"type": "array", "operation": "exists", "singleValue": true}}, {"id": "d66e3107-6ac8-4afd-9fbf-589e9b92e5db", "leftValue": "={{ $json.Data.AddMsgs[0].CreateTime.toDateTime('s') }}", "rightValue": "={{ $now.minus(1, 'min') }}", "operator": {"type": "dateTime", "operation": "after"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.filter", "typeVersion": 2.2, "position": [220, 420], "id": "0d862709-f0d3-435a-b1a7-186d1b779408", "name": "过滤历史消息2"}, {"parameters": {"assignments": {"assignments": [{"id": "caa5d346-a4c9-4f3c-bb2c-1e00c168cb78", "name": "msg_id", "value": "={{ $json.Data.AddMsgs[0].MsgId }}", "type": "number"}, {"id": "944ce9de-05da-4e48-bcf3-04c673cf0b72", "name": "msg_type", "value": "={{ $json.Data.AddMsgs[0].MsgType }}", "type": "number"}, {"id": "8d4e85a7-69cc-4e41-b5bf-a719fb14cdf1", "name": "timestamp", "value": "={{ $json.Data.AddMsgs[0].CreateTime }}", "type": "number"}, {"id": "da051b4b-52c5-4c01-a9db-f3a461b8353a", "name": "is_self_message", "value": "={{ $json.Data.AddMsgs[0].FromUserName.string == $json.Data.Wxid }}", "type": "boolean"}, {"id": "********-f0cc-46d6-b87f-e194d48cd89d", "name": "wxid", "value": "={{ $json.Data.Wxid }}", "type": "string"}, {"id": "9e645f61-65da-4cd8-b7db-dbc21e769d57", "name": "uuid", "value": "=", "type": "string"}, {"id": "9ba21441-f0fd-43b8-9e83-e513db595255", "name": "from_user_name", "value": "={{ $json.Data.AddMsgs[0].FromUserName.string }}", "type": "string"}, {"id": "99d9a442-7b20-4097-9e9c-0c9eac1946cf", "name": "to_user_name", "value": "={{ $json.Data.AddMsgs[0].ToUserName.string }}", "type": "string"}, {"id": "149259ac-7a2c-4499-aa12-06e0738382b3", "name": "content", "value": "={{ $json.Data.AddMsgs[0].Content.string }}", "type": "string"}, {"id": "58ada1c7-c663-450f-aa6c-c886056039f5", "name": "push_content", "value": "={{ $json.Data.AddMsgs[0].PushContent }}", "type": "string"}, {"id": "d755058c-6fe6-488f-a633-a91b198ef9d8", "name": "msg_source", "value": "={{ $json.Data.AddMsgs[0].MsgSource }}", "type": "string"}, {"id": "2e24b6ba-8670-47e1-b7ea-6d8da64e8db9", "name": "ver", "value": 2, "type": "number"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [440, 420], "id": "d9eb83ce-cfc2-4ffb-afa5-41bf23e07680", "name": "整理2", "notes": "整理"}, {"parameters": {"mode": "runOnceForEachItem", "jsCode": "// Add a new field called 'myNewField' to the JSON of the item\nif ($json.from_user_name.endsWith(\"@chatroom\")) {\n  // $json.is_group = true;\n  $json.group_id = $json.from_user_name;\n  const match = $json.content.match(/^([^:]+):([\\s\\S]*)/);\n  $json.from_user_name = match ? match[1] : \"\";\n  $json.content = match ? match[2].trim() : \"\";\n\n  // 1. 检测是否包含 <atuserlist>\n  if (/<atuserlist>/.test($json.msg_source)) {\n    // 2. 提取 ID（CDATA 内容）\n    const idMatch = $json.msg_source.match(\n      /<atuserlist><!\\[CDATA\\[([^\\]]+)\\]\\]><\\/atuserlist>/,\n    );\n    let idList = [];\n    if (idMatch) {\n      // 2. 按逗号拆分，并去除每个 ID 的首尾空格\n      idList = idMatch[1].split(\",\").map((id) => id.trim());\n    }\n    $json.at_list = idList;\n  }\n\n  // 3. 提取 membercount\n  const memberCountMatch = $json.msg_source.match(\n    /<membercount>(\\d+)<\\/membercount>/,\n  );\n  $json.member_count = memberCountMatch ? parseInt(memberCountMatch[1]) : 0;\n}\nreturn $input.item;\n"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1100, 220], "id": "97720103-c9e9-4132-9e97-47c0f235a944", "name": "处理群组"}, {"parameters": {"method": "POST", "url": "=http://172.19.213.236:9312/recv/{{ $json.wxid }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={{ $json }}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1320, 220], "id": "a5fb6617-aa85-4ec7-95e4-fb4960b748f7", "name": "HTTP Request"}, {"parameters": {"constantsKeyName": "wechat_config"}, "type": "n8n-nodes-globals.globalConstants", "typeVersion": 1, "position": [920, -20], "id": "0364823c-a3a4-413c-8cf8-fc6c2568f1bd", "name": "Global Constants", "credentials": {"globalConstantsApi": {"id": "ODfffzYcCxaPiLWy", "name": "Global Constants account"}}}], "pinData": {}, "connections": {"MQ V1": {"main": [[{"node": "过滤历史消息", "type": "main", "index": 0}]]}, "MQ V2": {"main": [[{"node": "过滤历史消息2", "type": "main", "index": 0}]]}, "HTTP Request - Get All Queues": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "HTTP Request - <PERSON>urge Queue", "type": "main", "index": 0}]]}, "Schedule Trigger": {"main": [[{"node": "HTTP Request - Get All Queues", "type": "main", "index": 0}]]}, "HTTP Request - Purge Queue": {"main": [[]]}, "MQ V1.5": {"main": [[{"node": "过滤历史消息1", "type": "main", "index": 0}]]}, "整理": {"main": [[{"node": "MSG_ID", "type": "main", "index": 0}]]}, "过滤历史消息": {"main": [[{"node": "整理", "type": "main", "index": 0}]]}, "MSG_TYPE": {"main": [[], [{"node": "处理群组", "type": "main", "index": 0}], [], [{"node": "处理群组", "type": "main", "index": 0}]]}, "MSG_ID": {"main": [[{"node": "MSG_TYPE", "type": "main", "index": 0}]]}, "过滤历史消息1": {"main": [[{"node": "整理1", "type": "main", "index": 0}]]}, "整理1": {"main": [[{"node": "MSG_ID", "type": "main", "index": 0}]]}, "过滤历史消息2": {"main": [[{"node": "整理2", "type": "main", "index": 0}]]}, "整理2": {"main": [[{"node": "MSG_ID", "type": "main", "index": 0}]]}, "处理群组": {"main": [[{"node": "HTTP Request", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1", "saveDataSuccessExecution": "none", "saveManualExecutions": false, "callerPolicy": "workflowsFromSameOwner"}, "versionId": "1efd3dec-2b14-4762-9c1b-c9c43807372f", "meta": {"templateCredsSetupCompleted": true, "instanceId": "eea60c48dc04980cf2fc958245a06d42777476423bf842f5def3a9492c8e02fa"}, "id": "0hw63kgPLtKlmfuo", "tags": [{"createdAt": "2025-08-02T07:50:05.541Z", "updatedAt": "2025-08-02T07:50:05.541Z", "id": "Bm7ICjuopgvqwWcU", "name": "wechat_bot"}]}