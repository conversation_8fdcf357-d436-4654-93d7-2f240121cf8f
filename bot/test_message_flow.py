#!/usr/bin/env python3
"""
测试消息处理流程
"""
import asyncio
import json
import time
from typing import Dict, Any

from message_processor import MessageProcessor
from message_filter import MessageFilter
from group_processor import GroupProcessor
from http_client import HTTPClient


async def test_message_flow():
    """测试完整的消息处理流程"""
    print("🚀 开始测试消息处理流程...")
    
    # 初始化组件
    message_processor = MessageProcessor()
    message_filter = MessageFilter()
    group_processor = GroupProcessor()
    
    # 测试数据
    test_messages = [
        # 版本1普通消息
        {
            "data": {
                "msg_id": 12345,
                "msg_type": 1,
                "create_time": int(time.time()),
                "is_self_message": False,
                "account_wxid": "test_wxid",
                "account_uuid": "test_uuid",
                "from_user_name": "user123",
                "to_user_name": "bot456",
                "content": "这是一条普通消息",
                "push_content": "",
                "msg_source": ""
            },
            "queue": "wechatpadpro",
            "version": 1
        },
        # 版本1群组消息
        {
            "data": {
                "msg_id": 12346,
                "msg_type": 1,
                "create_time": int(time.time()),
                "is_self_message": False,
                "account_wxid": "test_wxid",
                "account_uuid": "test_uuid",
                "from_user_name": "group123@chatroom",
                "to_user_name": "bot456",
                "content": "user456:这是一条群组消息 @user789",
                "push_content": "",
                "msg_source": "<atuserlist><![CDATA[user789,user101]]></atuserlist><membercount>50</membercount>"
            },
            "queue": "wechatpadpro",
            "version": 1
        },
        # 版本1.5消息
        {
            "data": {
                "AddMsgs": [{
                    "msg_id": 12347,
                    "msg_type": 1,
                    "create_time": int(time.time()),
                    "from_user_name": {"str": "user789"},
                    "to_user_name": {"str": "bot456"},
                    "content": {"str": "这是版本1.5的消息"},
                    "push_content": "",
                    "msg_source": ""
                }],
                "userName": "test_wxid",
                "UUID": "test_uuid_v15"
            },
            "queue": "wx_msg",
            "version": 1
        },
        # 自己的消息（应该被过滤）
        {
            "data": {
                "msg_id": 12348,
                "msg_type": 1,
                "create_time": int(time.time()),
                "is_self_message": True,
                "account_wxid": "test_wxid",
                "account_uuid": "test_uuid",
                "from_user_name": "test_wxid",
                "to_user_name": "user123",
                "content": "这是我自己发的消息",
                "push_content": "",
                "msg_source": ""
            },
            "queue": "wechatpadpro",
            "version": 1
        }
    ]
    
    print(f"📝 准备测试 {len(test_messages)} 条消息")
    
    processed_count = 0
    filtered_count = 0
    
    for i, test_msg in enumerate(test_messages, 1):
        print(f"\n--- 测试消息 {i} ---")
        
        try:
            # 1. 标准化消息
            message = message_processor.normalize_message(
                test_msg["data"], 
                test_msg["queue"], 
                test_msg["version"]
            )
            print(f"✓ 消息标准化成功: msg_id={message.msg_id}, content='{message.content[:20]}...'")
            
            # 2. 过滤和去重
            if not message_filter.should_process_message(message):
                print("⚠️ 消息被过滤（历史消息或重复消息）")
                filtered_count += 1
                continue
            
            # 3. 消息分类
            category = message_processor.categorize_message(message)
            print(f"✓ 消息分类: {category.category}, 需要处理: {category.should_process}")
            
            if not category.should_process:
                print("⚠️ 消息类型不需要处理")
                filtered_count += 1
                continue
            
            # 4. 群组消息处理
            if category.category in ["消息", "表情"]:
                processed_message = group_processor.process_group_message(message)
                if processed_message.group_id:
                    print(f"✓ 群组消息处理: group_id={processed_message.group_id}, "
                          f"from={processed_message.from_user_name}, "
                          f"at_list={processed_message.at_list}")
                else:
                    processed_message = message
                    print("✓ 普通消息，无需群组处理")
            else:
                processed_message = message
            
            # 5. 模拟HTTP转发（不实际发送）
            print(f"✓ 模拟HTTP转发到: /recv/{processed_message.wxid}")
            print(f"  消息内容: {processed_message.content}")
            
            processed_count += 1
            
        except Exception as e:
            print(f"✗ 处理消息失败: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n📊 测试结果:")
    print(f"  总消息数: {len(test_messages)}")
    print(f"  成功处理: {processed_count}")
    print(f"  被过滤: {filtered_count}")
    
    # 显示过滤器统计
    stats = message_filter.get_stats()
    print(f"  过滤器统计: {stats}")
    
    print("\n✅ 消息处理流程测试完成！")


if __name__ == "__main__":
    asyncio.run(test_message_flow())
