#!/usr/bin/env python3
"""
测试程序启动流程（不连接真实服务）
"""
import asyncio
import sys
from unittest.mock import AsyncMock, patch

from main import WeChatBotReceiver


async def test_startup():
    """测试启动流程"""
    print("🚀 测试程序启动流程...")
    
    receiver = WeChatBotReceiver()
    
    # 模拟各个组件的启动方法
    with patch.object(receiver.rabbitmq_client, 'connect', new_callable=AsyncMock) as mock_connect, \
         patch.object(receiver.rabbitmq_client, 'start_all_consumers', new_callable=AsyncMock) as mock_consumers, \
         patch.object(receiver.http_client, 'start', new_callable=AsyncMock) as mock_http_start, \
         patch.object(receiver.queue_cleaner, 'start', new_callable=AsyncMock) as mock_cleaner_start:
        
        try:
            print("✓ 开始启动各个组件...")
            
            # 模拟启动过程
            await receiver.rabbitmq_client.connect()
            print("✓ RabbitMQ客户端连接成功（模拟）")
            
            await receiver.http_client.start()
            print("✓ HTTP客户端启动成功（模拟）")
            
            await receiver.queue_cleaner.start()
            print("✓ 队列清理器启动成功（模拟）")
            
            # 模拟消息处理
            test_data = {
                "msg_id": 12345,
                "msg_type": 1,
                "create_time": **********,
                "is_self_message": False,
                "account_wxid": "test_wxid",
                "account_uuid": "test_uuid",
                "from_user_name": "user123",
                "to_user_name": "bot456",
                "content": "测试消息",
                "push_content": "",
                "msg_source": ""
            }
            
            print("✓ 测试消息处理...")
            await receiver.handle_message(test_data, "wechatpadpro", 1)
            print("✓ 消息处理成功（模拟HTTP转发会失败，这是正常的）")
            
            print("✅ 启动流程测试完成！")
            
        except Exception as e:
            print(f"✗ 启动测试失败: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_startup())
