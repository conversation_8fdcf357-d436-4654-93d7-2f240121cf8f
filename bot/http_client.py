"""
HTTP客户端
"""
import asyncio
from typing import Dict, Any
import aiohttp
from loguru import logger

from config import settings
from models import StandardMessage


class HTTPClient:
    """HTTP客户端"""
    
    def __init__(self):
        self.session = None
        self.timeout = aiohttp.ClientTimeout(total=30)
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        self.session = aiohttp.ClientSession(timeout=self.timeout)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        if self.session:
            await self.session.close()
    
    async def start(self):
        """启动HTTP客户端"""
        if not self.session:
            self.session = aiohttp.ClientSession(timeout=self.timeout)
        logger.info("HTTP client started")
    
    async def stop(self):
        """停止HTTP客户端"""
        if self.session:
            await self.session.close()
            self.session = None
        logger.info("HTTP client stopped")
    
    async def forward_message(self, message: StandardMessage) -> bool:
        """转发消息到HTTP接口"""
        if not self.session:
            logger.error("HTTP session not initialized")
            return False
        
        # 构建URL
        url = f"{settings.forward_base_url}/{message.wxid}"
        
        # 准备请求数据
        data = message.model_dump()
        
        try:
            headers = {
                'Content-Type': 'application/json'
            }
            
            async with self.session.post(url, json=data, headers=headers) as response:
                if response.status == 200:
                    logger.debug(f"Successfully forwarded message {message.msg_id} to {url}")
                    return True
                else:
                    response_text = await response.text()
                    logger.error(f"Failed to forward message {message.msg_id} to {url}: "
                               f"status={response.status}, response={response_text}")
                    return False
                    
        except asyncio.TimeoutError:
            logger.error(f"Timeout forwarding message {message.msg_id} to {url}")
            return False
        except aiohttp.ClientError as e:
            logger.error(f"Client error forwarding message {message.msg_id} to {url}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error forwarding message {message.msg_id} to {url}: {e}")
            return False
    
    async def forward_message_with_retry(self, message: StandardMessage, max_retries: int = 3) -> bool:
        """带重试的消息转发"""
        for attempt in range(max_retries):
            try:
                success = await self.forward_message(message)
                if success:
                    return True
                
                if attempt < max_retries - 1:
                    wait_time = 2 ** attempt  # 指数退避
                    logger.warning(f"Retrying message {message.msg_id} in {wait_time}s (attempt {attempt + 1}/{max_retries})")
                    await asyncio.sleep(wait_time)
                    
            except Exception as e:
                logger.error(f"Error in retry attempt {attempt + 1} for message {message.msg_id}: {e}")
                if attempt < max_retries - 1:
                    await asyncio.sleep(2 ** attempt)
        
        logger.error(f"Failed to forward message {message.msg_id} after {max_retries} attempts")
        return False
